<?php

use Illuminate\Support\Facades\Route;

if(env('APP_ENV')=='local'){
    Route::get('/plan-image/{fold1?}/{fold2?}/{file?}', [App\Http\Controllers\ImageController::class, 'GetPlan']);
}
// foreach ( as $domain) {
    Route::domain(config('tenancy.super_admin_central_domain'))->group(function () {
        Route::get('/{any}', function () {
            return view('index');
        })->where('any', '.*');
    });
// }



