<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;


Route::domain(config('tenancy.super_admin_central_domain'))->group(function () {
    Route::controller(\App\Http\Controllers\Central\Admin\AuthController::class)->group(function () {
        Route::post('login', 'AdminLogin');
    });

    Route::controller(\App\Http\Controllers\PaypalPaymentMethodController::class)->group(function () {
        Route::post('paypal/vault/token/create', 'VaultToken');
        Route::get('paypal/vault/payment/token/create/{vault_id}', 'VaultPaymentToken');
    });

    Route::controller(\App\Http\Controllers\PharmacyPrecheckoutController::class)->group(function () {
        Route::get('pharmacy/pre-checkout/details', 'PharmacyPreCheckoutDetails');
        Route::post('validate/pharmacy/subdomain','ValidatePharmacySubdomain');
        Route::post('new/pharmacy/request','NewPharmacyRequest');
    });

    Route::middleware('sanctum-cookie','auth-sanctum')->group(function () {
        Route::controller(\App\Http\Controllers\Central\Admin\AuthController::class)->group(function () {
            Route::get('verify-token', 'VerifyToken');
        });
    });

    Route::middleware('sanctum-cookie','auth-sanctum','verify-2fa')->group(function () {
        Route::controller(\App\Http\Controllers\Central\Admin\AuthController::class)->group(function () {
            // Route::get('verify-token', 'VerifyToken');
            Route::post('change-password', 'ChangePassword');
            Route::get('logout', 'logout');
        });

        Route::controller(\App\Http\Controllers\Central\Admin\PharmacyController::class)->group(function () {
            Route::post('add/pharmacy', 'AddPharmacy');
            Route::post('list/pharmacy', 'ListPharmacy');
            Route::post('edit/pharmacy', 'EditPharmacy');
            Route::get('view/pharmacy/{id}', 'ViewPharmacy');
            Route::get('update/pharmacy/status/{id}', 'UpdatePharmacyStatus');
            Route::get('resent/pharmacy/invitation/{id}', 'ResendInvitation');
        });

        Route::controller(App\Http\Controllers\Central\Admin\TwoFactorAuthController::class)->group(function () {
            Route::get('/2fa/status', 'index');
            Route::post('/2fa/enable', 'enable');
            Route::post('/2fa/verify', 'verify');
            Route::post('/2fa/disable', 'disable');
        });

        Route::controller(App\Http\Controllers\Central\Admin\PlanSettingController::class)->group(function () {
            Route::get('/plan/details', 'GetPlanDetails');
            Route::post('/update/plan', 'UpdatePlan');
            // Route::post('/update/plan/image', 'UpdatePlanImage');
        });
    });
});
