import type { LucideIcon } from 'lucide-vue-next'
import { LayoutDashboard, Building2, Settings2 } from 'lucide-vue-next'

export interface SubMenuItem {
  title: string
  routeName: string
}

export interface MenuItem {
  title: string
  routeName?: string
  icon?: LucideIcon
  isActive?: boolean
  items?: SubMenuItem[]
}

export interface MenuGroup {
  label?: string
  items: MenuItem[]
}

export type MenuConfig = MenuGroup[]

export const menuItems: MenuConfig = [
  {
    label: 'Platform',
    items: [
      {
        title: 'Dashboard',
        routeName: 'dashboard',
        icon: LayoutDashboard,
        isActive: true,
      },
      {
        title: 'Pharmacies',
        routeName: 'pharmacies-list',
        icon: Building2,
      },
      {
        title: 'Plan Settings',
        routeName: 'plan-settings',
        icon: Settings2,
      },
    ],
  },
]
