<script setup lang="ts">
import { processErrors, isEmptyObject } from '@/lib'
import type { ApiResponse } from '@/types/api'
import { apiClient } from '@tenant/composables'
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2 } from 'lucide-vue-next'
import { useGlobalSettingsStore } from '@tenant/stores'
import { vMaska } from 'maska/vue'

const route = useRoute()
const router = useRouter()
const globalStore = useGlobalSettingsStore()

const userLeadSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Invalid email address').min(1, 'Email is required'),
  phone_number: z.string().min(1, 'Phone number is required'),
  product_item_id: z.string().min(1, 'Product item is required'),
})

type UserLead = z.infer<typeof userLeadSchema>

const formError = ref<string | null>(null)
const isSubmitting = ref(false)
const xpedicareUrl = ref<string>('')

const form = useForm<UserLead>({
  validationSchema: toTypedSchema(userLeadSchema),
  initialValues: {
    name: '',
    email: '',
    phone_number: '',
    product_item_id: '',
  },
})

onMounted(() => {
  const productItemId = route.query.product_item_id as string
  const redirectUrl = route.query.xpedicare_url as string

  if (productItemId && redirectUrl) {
    form.setFieldValue('product_item_id', productItemId)
    xpedicareUrl.value = redirectUrl
  } else {
    router.back()
  }
})

function applyBackendErrors(errors: Record<string, any>) {
  if (!errors || typeof errors !== 'object') return

  Object.entries(errors).forEach(([field, messages]) => {
    if (Array.isArray(messages) && messages.length > 0) {
      form.setFieldError(field as keyof UserLead, messages[0])
    } else if (typeof messages === 'string') {
      form.setFieldError(field as keyof UserLead, messages)
    }
  })
}

const onSubmit = form.handleSubmit(async (values) => {
  if (isSubmitting.value) return

  isSubmitting.value = true
  formError.value = null

  try {
    const payload = {
      ...values,
      phone_number: values.phone_number.replace(/\D/g, ''),
    }

    const response = await apiClient.post<ApiResponse>('/user-product-lead', payload)

    if (response.data.status === 200) {
      if (xpedicareUrl.value) {
        window.location.href = xpedicareUrl.value
      }
    } else if (response.data.errors && !isEmptyObject(response.data.errors)) {
      applyBackendErrors(response.data.errors)
    } else {
      formError.value = response.data.message || 'Something went wrong. Please try again.'
    }
  } catch (error: any) {
    // Handle network errors or other exceptions
    if (error.response?.data?.errors && !isEmptyObject(error.response.data.errors)) {
      applyBackendErrors(error.response.data.errors)
    } else {
      formError.value = processErrors(error, 'Something went wrong. Please try again.')
    }
  } finally {
    isSubmitting.value = false
  }
})
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <div class="py-6 flex justify-center">
      <img
        :src="globalStore.settings?.app_logo"
        :alt="globalStore.settings?.app_name"
        class="max-h-12 max-w-36 sm:max-w-56"
      />
    </div>
    <div class="max-w-lg mx-auto px-4">
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">Start Your Treatment</h1>
        <p class="text-gray-600">
          Please fill out this form to begin your personalized treatment journey
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Personal Information</CardTitle>
          <CardDescription>
            We need some basic information to get you started with your treatment.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form @submit.prevent="onSubmit" class="space-y-6">
            <Alert v-if="formError" variant="destructive">
              <AlertDescription>{{ formError }}</AlertDescription>
            </Alert>

            <div class="space-y-4">
              <FormField name="name" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Full Name *</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      type="text"
                      placeholder="Enter your full name"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <FormField name="email" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Email Address *</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      type="email"
                      placeholder="Enter your email address"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <FormField name="phone_number" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Phone Number *</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      v-maska
                      data-maska="(###) ###-####"
                      type="tel"
                      placeholder="Enter your phone number"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>
            </div>

            <div class="pt-4">
              <Button
                type="submit"
                :disabled="isSubmitting"
                class="w-full bg-black text-white hover:bg-gray-800 disabled:opacity-50"
              >
                <Loader2 v-if="isSubmitting" class="w-4 h-4 mr-2 animate-spin" />
                {{ isSubmitting ? 'Submitting...' : 'Continue to Treatment' }}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      <!-- <div class="text-center mt-6">
        <p class="text-sm text-gray-500">
          By continuing, you agree to our terms of service and privacy policy.
        </p>
      </div> -->
    </div>
  </div>
</template>
