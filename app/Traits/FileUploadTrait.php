<?php

namespace App\Traits;

use Illuminate\Support\Facades\Storage;

trait FileUploadTrait
{
    public static function PlanImageUploadFunction($base64Image, $destination_path)
    {
        $bucket = env('APP_ENV');

        $header = explode(';', $base64Image)[0];
        $mimePart = explode(':', $header)[1] ?? '';
        $originalExt = explode('/', $mimePart)[1] ?? '';
        $originalExt = strtolower($originalExt);

        $extension = $originalExt;

        $base64Data = substr($base64Image, strpos($base64Image, ',') + 1);

        $imgFile = base64_decode($base64Data);
        if ($imgFile === false) {
            throw new \Exception('Base64 decode failed');
        }

        $file_name = uniqid().'_'.rand(100000,999999).'.'.$extension;
        $file_path = $destination_path.'/'.$file_name;

        if($bucket=='local'){
            Storage::disk('local')->put($file_path, $imgFile);
        } else {
            Storage::disk('s3')->put($file_path, $imgFile);
        }
        return $file_path;
    }

    public static function UploadFile($base64Image, $destination_path)
    {
        $bucket = env('APP_ENV');

        $header = explode(';', $base64Image)[0];
        $mimePart = explode(':', $header)[1] ?? '';
        $originalExt = explode('/', $mimePart)[1] ?? '';
        $originalExt = strtolower($originalExt);

        $extension = $originalExt;

        $base64Data = substr($base64Image, strpos($base64Image, ',') + 1);

        $imgFile = base64_decode($base64Data);
        if ($imgFile === false) {
            throw new \Exception('Base64 decode failed');
        }

        $file_name = uniqid().'_'.rand(100000,999999).'.'.$extension;
        $file_path = $destination_path.'/'.$file_name;

        if($bucket=='local'){
            Storage::disk('local')->put($file_path, $imgFile);
        } else {
            Storage::disk('s3')->put($file_path, $imgFile);
        }
        return $file_path;
    }

    public static function UploadBase64Image($base64Image, $destination_path,$is_logo)
    {
        $bucket = env('APP_ENV');
        // $destination_path = $bucket.'/admin/'.$folder;

        $header = explode(';', $base64Image)[0];
        $mimePart = explode(':', $header)[1] ?? '';
        $originalExt = explode('/', $mimePart)[1] ?? '';
        $originalExt = strtolower($originalExt);

        // $extension = in_array($originalExt, ['png', 'ico']) ? $originalExt : 'jpg';
        $extension = $originalExt;

        $base64Data = substr($base64Image, strpos($base64Image, ',') + 1);

        $imgFile = base64_decode($base64Data);
        if ($imgFile === false) {
            throw new \Exception('Base64 decode failed');
        }

        if($is_logo){
            $file_name = 'logo-'.rand(100000,999999);
        } else {
            $file_name = 'favicon-'.rand(100000,999999);
        }
        $fileName = $file_name.'.' . $extension;
        $file_path = "{$destination_path}/{$fileName}";

        if($bucket=='local'){
            Storage::disk('local')->put($file_path, $imgFile);
        } else {
            Storage::disk('s3')->put($file_path, $imgFile);
        }
        return $file_path;
    }

    public static function UploadLandingPageImages($base64Image)
    {
        // dd($folder);
        $bucket = env('APP_ENV');
        $destination_path = 'frontend/images';

        $header = explode(';', $base64Image)[0];
        $mimePart = explode(':', $header)[1] ?? '';
        $originalExt = explode('/', $mimePart)[1] ?? '';
        $originalExt = strtolower($originalExt);

        $extension = in_array($originalExt, ['png','gif']) ? $originalExt : 'jpg';

        $base64Data = substr($base64Image, strpos($base64Image, ',') + 1);

        // Decode
        $imgFile = base64_decode($base64Data);
        if ($imgFile === false) {
            throw new \Exception('Base64 decode failed');
        }

        $fileName = uniqid().rand(100000,9999999). '.' . $extension;
        $file_path = "{$destination_path}/{$fileName}";

        if($bucket=='local'){
            Storage::disk('local')->put($file_path, $imgFile);
        } else {
            Storage::disk('s3')->put($file_path, $imgFile);
        }
        return $file_path;
    }

    public static function RemoveSpaceAndSetUnderscore($value){
        // return strtolower(str_replace(' ','_',$value));
        $result = strtolower(preg_replace('/[^a-z0-9]+/', '_', $value));
        return trim($result, '_'); // removes underscores from start/end
    }

    public static function GetFilePath($path,$type){
        if(env('APP_ENV')=='local'){
            if(tenant()){
                $tenant_path = 'pharmacy-assets/'.tenant()->id;
                $image_path = $tenant_path.'/'.$path;
            } else {
                $image_path = $path;
            }
            // dd(Storage::disk('local')->exists($path));
            if(Storage::disk('local')->exists($path)){
                if($type=='main_category'){
                    return url('/categories/'.$image_path);
                } else if($type=='products'){
                    return url('/products/'.$image_path);
                } else if($type=='app_logo'){
                    return url('/images/'.$image_path);
                } else if($type=='images'){
                    return url('/images/'.$image_path);
                } else if($type=='plans'){
                    return url('/plan-image/'.$image_path);
                } else if($type=='frontend_images'){
                    return url('/frontend-images/'.$image_path);
                }
            }
        } else {
            if(tenant()){
                $tenant_path = 'pharmacy-assets/'.tenant()->id;
                $image_path = $tenant_path.'/'.$path;
            } else {
                $image_path = $path;
            }
            return config('filesystems.disks.s3.url').'/'.$image_path;
        }

    }
}
